using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Features.MobiFoneInvoice.Commands;
using Applications.Features.MobiFoneInvoice.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

/// <summary>
/// Controller cho tích hợp MobiFone Invoice API
/// </summary>
// [Route("api/[controller]")]
// [ApiController]
public class MobiFoneInvoiceController(IMediator mediator) : BaseApiController
{

    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <returns>Token và thông tin user</returns>
    [HttpPost("login")]
    public async Task<IActionResult> LoginAsync([FromBody] LoginRequest request)
    {
        var command = new LoginCommand(request);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Danh sách thông tin dải ký hiệu</returns>
    [HttpGet("data-references")]
    public async Task<IActionResult> GetDataReferencesAsync(
        [FromQuery] GetDataReferencesRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var query = new GetDataReferencesQuery(request, token, maDvcs);
        var result = await mediator.Send(query);
        return Ok(result);
    }

    #region Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ)

    /// <summary>
    /// a. Hóa đơn Giá trị gia tăng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("create-invoice")]
    public async Task<IActionResult> CreateInvoiceAsync(
        [FromBody] SaveListHoadon78Request request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new CreateInvoiceCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    /// b. Tạo mới Hóa đơn Bán hàng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    [HttpPost("create-sales-invoice")]
    public async Task<IActionResult> CreateSalesInvoiceAsync(
        [FromBody] SaveListHoadonBanHangRequest request,
        [FromHeader(Name = "X-Token")] string token,
        [FromHeader(Name = "X-MaDvcs")] string maDvcs)
    {
        var command = new CreateSalesInvoiceCommand(request, token, maDvcs);
        var result = await mediator.Send(command);
        return Ok(result);
    }
    
    #endregion
}
