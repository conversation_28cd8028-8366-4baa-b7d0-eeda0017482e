using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Shared.Responses;

namespace Applications.Interfaces.Services;

/// <summary>
/// Interface cho service tích hợp MobiFone Invoice API
/// </summary>
public interface IMobiFoneInvoiceService
{
    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice để lấy token
    /// </summary>
    /// <param name="request">Thông tin đăng nhập</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin token và user</returns>
    Task<Response<LoginResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn
    /// </summary>
    /// <param name="request">Thông tin request</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Danh sách thông tin dải ký hiệu</returns>
    Task<Response<GetDataReferencesResponse>> GetDataReferencesAsync(
        GetDataReferencesRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ) - Hóa đơn Giá trị gia tăng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadon78Response>>> CreateInvoiceAsync(
        SaveListHoadon78Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng
    /// </summary>
    /// <param name="request">Thông tin hóa đơn bán hàng cần tạo (Raw DTO theo chuẩn MobiFone)</param>
    /// <param name="token">Token từ login</param>
    /// <param name="maDvcs">Mã đơn vị</param>
    /// <param name="cancellationToken">Token hủy bỏ</param>
    /// <returns>Thông tin hóa đơn bán hàng đã tạo (Raw DTO theo chuẩn MobiFone)</returns>
    Task<Response<List<SaveListHoadonBanHangResponse>>> CreateSalesInvoiceAsync(
        SaveListHoadonBanHangRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default);
}
