using System.Text.Json.Serialization;

namespace Applications.DTOs.MobiFoneInvoice.Common;

/// <summary>
/// Class chứa các trường đặc biệt cho Hóa đơn Giá trị gia tăng (GTGT)
/// <PERSON>ế thừa từ NormalInvoiceHeaderFields và thêm các trường riêng biệt
/// </summary>
public class GTGTInvoiceFields : NormalInvoiceHeaderFields
{
    /// <summary>
    /// Tổng giảm trừ không chịu thuế (Đặc biệt cho Hóa đơn GTGT)
    /// Kiểu: Number
    /// </summary>
    [JsonPropertyName("TGTKCThue")]
    public decimal? TGTKCThue { get; set; }

    /// <summary>
    /// Tổng giảm trừ khác (Đặc biệt cho Hóa đơn GTGT)
    /// Kiểu: Number, Bắt buộc nếu có
    /// </summary>
    [JsonPropertyName("TGTKhac")]
    public decimal? TGTKhac { get; set; }

    /// <summary>
    /// Danh sách chi tiết hóa đơn GTGT
    /// </summary>
    [JsonPropertyName("details")]
    public List<InvoiceDetailWrapper<GTGTInvoiceDetailFields>> details { get; set; } = new();

    /// <summary>
    /// Danh sách các loại tiền phí/lệ phí
    /// </summary>
    [JsonPropertyName("hoadon68_phi")]
    public List<InvoiceFeeWrapper<InvoiceFeeFields>>? hoadon68_phi { get; set; }

    /// <summary>
    /// Danh sách các trường thẻ khác bổ sung
    /// </summary>
    [JsonPropertyName("hoadon68_khac")]
    public List<InvoiceOtherWrapper<InvoiceOtherFields>>? hoadon68_khac { get; set; }
}

/// <summary>
/// Chi tiết hóa đơn GTGT - kế thừa từ InvoiceDetailFields
/// Cho Hóa đơn GTGT, tsuat là bắt buộc (khác với Hóa đơn Bán hàng)
/// </summary>
public class GTGTInvoiceDetailFields : InvoiceDetailFields
{
    /// <summary>
    /// Mã loại thuế suất - BẮT BUỘC cho Hóa đơn GTGT
    /// 10: Thuế 10%, 8: Thuế 8%, 5: Thuế 5%, 0: Thuế 0%, -1: Không chịu thuế, -2: Không nộp thuế
    /// Kiểu: String, Độ dài: 20, Bắt buộc: X
    /// </summary>
    [JsonPropertyName("tsuat")]
    public new string tsuat { get; set; } = string.Empty;
}
