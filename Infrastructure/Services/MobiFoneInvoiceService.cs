using Applications.DTOs.MobiFoneInvoice.GetDataReferences;
using Applications.DTOs.MobiFoneInvoice.Login;
using Applications.DTOs.MobiFoneInvoice.CreateInvoice.Raws;
using Applications.DTOs.MobiFoneInvoice.CreateSalesInvoice.Raws;
using Applications.Interfaces.Services;
using Applications.Exceptions;
using Infrastructure.Configurations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Constants;
using Shared.Responses;
using System.Text;
using System.Text.Json;

namespace Infrastructure.Services;

/// <summary>
/// Service tích hợp MobiFone Invoice API
/// </summary>
public class MobiFoneInvoiceService(
    HttpClient httpClient,
    IOptions<MobiFoneInvoiceConfiguration> config,
    ILogger<MobiFoneInvoiceService> logger) : IMobiFoneInvoiceService
{
    private readonly MobiFoneInvoiceConfiguration _config = config.Value;
    private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        PropertyNameCaseInsensitive = true,
        WriteIndented = true
    };

    /// <summary>
    /// Lấy base URL dựa trên môi trường
    /// </summary>
    private string GetMobiFoneApiUrl()
    {
        return _config.GetBaseUrl();
    }

    /// <summary>
    /// Setup headers cho MobiFone Invoice API request
    /// </summary>
    private void SetMobiFoneHeaders(string? token = null, string? maDvcs = null)
    {
        httpClient.DefaultRequestHeaders.Clear();
        if (!string.IsNullOrEmpty(token) && !string.IsNullOrEmpty(maDvcs))
        {
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bear {token};{maDvcs}");
        }
    }

    /// <summary>
    /// Handle response chung cho MobiFone API calls
    /// </summary>
    private async Task<T> HandleMobiFoneApiResponse<T>(HttpResponseMessage response, string apiName, string endpoint) where T : class
    {
        var responseContent = await response.Content.ReadAsStringAsync();
        
        logger.LogInformation("MobiFone {ApiName} Response: {StatusCode} - {ResponseLength} chars",
            apiName,
            response.StatusCode,
            responseContent.Length);

        if (!response.IsSuccessStatusCode)
        {
            logger.LogWarning("MobiFone {ApiName} API returned non-success status: {StatusCode} - {Content}",
                apiName,
                response.StatusCode,
                responseContent);

            // Thử parse error response nếu có
            try
            {
                var errorResponse = JsonSerializer.Deserialize<LoginErrorResponse>(responseContent, _jsonOptions);
                if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.error))
                {
                    throw new MobiFoneApiResponseException(apiName, errorResponse.error);
                }
            }
            catch (JsonException)
            {
                // Ignore error parsing, use default message
            }

            throw new MobiFoneApiResponseException(apiName, (int)response.StatusCode);
        }

        try
        {
            var result = JsonSerializer.Deserialize<T>(responseContent, _jsonOptions);
            if (result == null)
            {
                logger.LogError("Failed to deserialize MobiFone {ApiName} response - result is null", apiName);
                throw new MobiFoneApiDeserializationException(apiName);
            }

            logger.LogInformation("MobiFone {ApiName} API call completed successfully", apiName);
            return result;
        }
        catch (JsonException ex)
        {
            var errorMessage = JsonSerializer.Deserialize<List<string>>(responseContent, _jsonOptions);
            
            if (errorMessage != null && errorMessage.Count > 0)
            {
                logger.LogError("JSON deserialization error for MobiFone {ApiName} API: {ErrorMessage}", apiName, string.Join(", ", errorMessage));
                // throw new MobiFoneApiDeserializationException(apiName, errorMessage);
            }
            
            logger.LogError(ex, "JSON deserialization error for MobiFone {ApiName} API", apiName);
            throw new MobiFoneApiDeserializationException(apiName, ex);
        }
    }

    /// <summary>
    /// Đăng nhập vào hệ thống MobiFone Invoice để lấy token
    /// </summary>
    public async Task<Response<LoginResponse>> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Attempting to login to MobiFone Invoice API for user: {Username}", request.Username);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Account/Login";

            // Tạo request body dựa trên môi trường
            object requestBody;
            if (_config.IsProduction)
            {
                requestBody = new
                {
                    tax_code = request.TaxCode,
                    username = request.Username,
                    password = request.Password
                };
            }
            else
            {
                requestBody = new
                {
                    username = request.Username,
                    password = request.Password,
                    ma_dvcs = "",
                    is_sso_login = 1
                };
            }

            var json = JsonSerializer.Serialize(requestBody, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            logger.LogInformation("Calling MobiFone Login API for Username: {Username} - Environment: {Environment}",
                request.Username,
                _config.Environment);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<LoginResponse>(response, "Login", endpoint);

            return new Response<LoginResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while logging in to MobiFone Invoice API for user: {Username}",
                request.Username);
            return new Response<LoginResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone Login API."
            };
        }
    }

    /// <summary>
    /// Lấy thông tin dải ký hiệu mẫu số hóa đơn
    /// </summary>
    public async Task<Response<GetDataReferencesResponse>> GetDataReferencesAsync(
        GetDataReferencesRequest request, 
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Getting data references with RefId: {RefId}", request.RefId);

            var apiUrl = GetMobiFoneApiUrl();
            var url = $"{apiUrl}/api/System/GetDataReferencesByRefId?refId={request.RefId}";

            // Thêm tax_code parameter cho production
            // if (!string.IsNullOrEmpty(_config.ProductionBaseUrl) && !string.IsNullOrEmpty(request.TaxCode))
            // {
            //     url += $"&tax_code={request.TaxCode}";
            // }
            
            url += $"&tax_code={request.TaxCode}";

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone GetDataReferences API - RefId: {RefId}", request.RefId);

            var response = await httpClient.GetAsync(url, cancellationToken);
            
            var invoiceTemplates = await HandleMobiFoneApiResponse<List<InvoiceTemplateInfo>>(response, "GetDataReferences", url);
            
            var result = new GetDataReferencesResponse
            {
                Data = invoiceTemplates
            };

            logger.LogInformation("Successfully retrieved {Count} invoice templates", invoiceTemplates.Count);
            return new Response<GetDataReferencesResponse>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting data references from MobiFone Invoice API: {ex}", ex.Message);
            return new Response<GetDataReferencesResponse>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone GetDataReferences API."
            };
        }
    }

    /// <summary>
    /// Tạo mới hóa đơn quy trình thường (tất cả hình thức HĐ) - Hóa đơn Giá trị gia tăng
    /// </summary>
    public async Task<Response<List<SaveListHoadon78Response>>> CreateInvoiceAsync(
        SaveListHoadon78Request request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadon78Response>>(response, "CreateInvoice", endpoint);

            logger.LogInformation("Successfully created invoice with response: {Count} items", result.Count);
            return new Response<List<SaveListHoadon78Response>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadon78Response>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateInvoice API."
            };
        }
    }

    /// <summary>
    /// Tạo mới Hóa đơn Bán hàng
    /// </summary>
    public async Task<Response<List<SaveListHoadonBanHangResponse>>> CreateSalesInvoiceAsync(
        SaveListHoadonBanHangRequest request,
        string token,
        string maDvcs,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Creating sales invoice with EditMode: {EditMode}, Data count: {DataCount}",
                request.editmode, request.data?.Count ?? 0);

            var apiUrl = GetMobiFoneApiUrl();
            var endpoint = $"{apiUrl}/api/Invoice68/SaveListHoadon78";

            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            SetMobiFoneHeaders(token, maDvcs);

            logger.LogInformation("Calling MobiFone CreateSalesInvoice API - EditMode: {EditMode}",
                request.editmode);

            var response = await httpClient.PostAsync(endpoint, content, cancellationToken);
            var result = await HandleMobiFoneApiResponse<List<SaveListHoadonBanHangResponse>>(response, "CreateSalesInvoice", endpoint);

            logger.LogInformation("Successfully created sales invoice with response ok: {Count} items", result.Count);
            return new Response<List<SaveListHoadonBanHangResponse>>(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while creating sales invoice in MobiFone Invoice API: {ex}", ex.Message);
            return new Response<List<SaveListHoadonBanHangResponse>>
            {
                Code = ErrorCodes.EXCEPTION_ERROR,
                Message = "An error occurred while calling MobiFone CreateSalesInvoice API."
            };
        }
    }
}
