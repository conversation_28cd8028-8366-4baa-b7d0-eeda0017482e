﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="ExternalSystems\**" />
        <Compile Remove="Persistences\UnitOfWorks\**" />
        <EmbeddedResource Remove="ExternalSystems\**" />
        <EmbeddedResource Remove="Persistences\UnitOfWorks\**" />
        <None Remove="ExternalSystems\**" />
        <None Remove="Persistences\UnitOfWorks\**" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.5" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Applications\Applications.csproj" />
        <ProjectReference Include="..\Core\Core.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Migrations\" />
    </ItemGroup>

</Project>
